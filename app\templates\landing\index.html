{% extends "main_layout.html" %}

{% block head_extra %}
<!-- AOS CSS -->
<link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
{% endblock %}

{% block content %}

<!-- Hero Section -->
<section class="hero-section py-5 landing-page">
    <div class="container">
        <div class="row align-items-center">
            <!-- Content remains unchanged -->
        </div>
    </div>
</section>

<!-- Innovative Modern Comparison Section -->
<section class="modern-comparison-section py-5 landing-page" style="background-color: #00A0A0; color: white; position: relative; overflow: hidden; margin-top: -100px; z-index: 1;">
    <div class="diagonal-split"></div>
    <div class="container">
        <div class="row align-items-center">
            <!-- Left side with text content -->
            <div class="col-lg-6 mb-5 mb-lg-0" data-aos="fade-up" data-aos-duration="1000">
                <h2 class="display-4 fw-bold mb-4">اتوماسیون حرفه‌ای محتوا با قدرت هوش مصنوعی</h2>
                <p class="lead mb-5">رومینکست، سیستم تمام‌اتومات تولید و مدیریت محتوا، تعامل با مخاطب و تحلیل رقبا — همه در یک پلتفرم هوش مصنوعی</p>
                
                <div class="d-flex flex-column flex-sm-row gap-3">
                    <a href="#contact" class="btn btn-light rounded-pill px-4 py-2"> شروع رایگان!</a>
                   
                </div>
            </div>
            
            <!-- Right side for illustration -->
            <div class="col-lg-6 text-center" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
                <div class="illustration-placeholder">
                    <!-- Circle background for the image -->
                    <div class="circle-bg">
                        <img src="/static/img/automation.png" alt="AI Content Automation" class="img-fluid">
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Simple Description Text Section -->
<section class="py-5 d-flex align-items-center justify-content-center" style="background-color: white; margin-top: -2px; position: relative; z-index: 0;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10 text-center" data-aos="fade-up" data-aos-duration="800">
                <h2 class="mb-3" style="font-size: 1.8rem; color: #00A0A0;">سیستم هوشمند برای تولید محتوا، پاسخ‌گویی و تحلیل رقبا</h2>
                <p class="text-muted mx-auto" style="max-width: 700px; font-size: 0.95rem;"></p>
                    رومینکست یک سامانه هوش مصنوعی تمام‌اتومات است که محتوای اختصاصی تولید می‌کند، پاسخ کامنت‌ها و دایرکت‌ها را می‌دهد، رقبا را تحلیل می‌کند و حضور دیجیتال شما را بدون نیاز به دخالت انسانی ارتقا می‌دهد. با رومینکست، مدیریت محتوا دیگر یک دغدغه نیست — بلکه یک مزیت رقابتی است.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Supported Platforms Section -->
<section class="platforms-section py-5" style="background-color: #f8f9fa;">
    <div class="container">
        <div class="row text-center mb-4">
            <div class="col-lg-8 mx-auto" data-aos="fade-up" data-aos-duration="800">
                <h2 class="fw-bold text-center w-100">
                    پلتفرم‌های پشتیبانی شده
                </h2>
                <p class="lead text-center text-muted mt-3">تولید محتوای هوشمند برای تمامی شبکه‌های اجتماعی و وب‌سایت‌ها</p>
            </div>
        </div>
        
        <div class="row justify-content-center text-center g-4" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="200">
            <!-- Instagram -->
            <div class="col-4 col-md-2 text-center">
                <div class="platform-icon instagram-icon mx-auto">
                    <i class="fab fa-instagram fa-2x"></i>
                </div>
                <p class="mt-2 mb-0">اینستاگرام</p>
            </div>
            
            <!-- Telegram -->
            <div class="col-4 col-md-2 text-center">
                <div class="platform-icon telegram-icon mx-auto">
                    <i class="fab fa-telegram fa-2x"></i>
                </div>
                <p class="mt-2 mb-0">تلگرام</p>
            </div>
            
            <!-- Twitter -->
            <div class="col-4 col-md-2 text-center">
                <div class="platform-icon twitter-icon mx-auto">
                    <i class="fab fa-twitter fa-2x"></i>
                </div>
                <p class="mt-2 mb-0">توییتر</p>
            </div>
            
            <!-- LinkedIn -->
            <div class="col-4 col-md-2 text-center">
                <div class="platform-icon linkedin-icon mx-auto">
                    <i class="fab fa-linkedin fa-2x"></i>
                </div>
                <p class="mt-2 mb-0">لینکدین</p>
            </div>
            
            <!-- Facebook -->
            <div class="col-4 col-md-2 text-center">
                <div class="platform-icon facebook-icon mx-auto">
                    <i class="fab fa-facebook fa-2x"></i>
                </div>
                <p class="mt-2 mb-0">فیسبوک</p>
            </div>
            
            <!-- Blog/Website -->
            <div class="col-4 col-md-2 text-center">
                <div class="platform-icon blog-icon mx-auto">
                    <i class="fas fa-rss fa-2x"></i>
                </div>
                <p class="mt-2 mb-0">وبلاگ</p>
            </div>
        </div>
        
        <style>
            .platform-icon {
                width: 70px;
                height: 70px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-bottom: 10px;
                color: white;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
            }
            
            .platform-icon:hover {
                transform: scale(1.1);
                box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
            }
            
            .col-4.col-md-2 {
                display: flex;
                flex-direction: column;
                align-items: center;
            }
            
            .instagram-icon { background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888); }
            .telegram-icon { background: #0088cc; }
            .twitter-icon { background: #1da1f2; }
            .linkedin-icon { background: #0077b5; }
            .facebook-icon { background: #4267B2; }
            .blog-icon { background: #00A0A0; }
        </style>
    </div>
</section>

<!-- AI-Powered Content Creation Section -->
<section class="ai-content-section py-5" style="background-color: white;">
    <div class="container">
        <div class="row align-items-center">
            <!-- Left side: Image placeholder -->
            <div class="col-lg-6 mb-4 mb-lg-0" data-aos="fade-right" data-aos-duration="1000">
                <div class="image-container position-relative" style="margin-top: -20px; margin-right: 20px;">
                    <img src="/static/img/ai-content-creation.png" alt="هوش مصنوعی در تولید محتوا" class="img-fluid">
                </div>
            </div>
            
            <!-- Right side: Content -->
            <div class="col-lg-6" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
                <h2 class="fw-bold mb-4">قدرت هوش مصنوعی در خدمت محتوای شما</h2>
                <p class="lead mb-4">رومینکست با استفاده از پیشرفته‌ترین الگوریتم‌های هوش مصنوعی، محتوای اختصاصی و جذاب برای کسب‌وکار شما تولید می‌کند.</p>
                
                <div class="features-list">
                    <div class="d-flex align-items-center mb-3">
                        <div class="feature-icon-small me-3 text-primary">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <p class="mb-0">تولید محتوای منحصر به فرد با سبک و لحن برند شما</p>
                    </div>
                    
                    <div class="d-flex align-items-center mb-3">
                        <div class="feature-icon-small me-3 text-primary">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <p class="mb-0">بهینه‌سازی خودکار محتوا برای هر پلتفرم اجتماعی</p>
                    </div>
                    
                    <div class="d-flex align-items-center mb-3">
                        <div class="feature-icon-small me-3 text-primary">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <p class="mb-0">تولید تصاویر جذاب با هوش مصنوعی متناسب با محتوا</p>
                    </div>
                    
                    <div class="d-flex align-items-center mb-4">
                        <div class="feature-icon-small me-3 text-primary">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <p class="mb-0">تحلیل روندها و پیشنهاد موضوعات پربازدید</p>
                    </div>
                </div>
             </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section id="features" class="features py-5 position-relative" style="background: inherit; overflow: hidden;">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-lg-8 mx-auto" data-aos="fade-up" data-aos-duration="800">
                <h2 class="fw-bold text-center w-100">
                    امکانات رومینکست
                </h2>
                <p class="lead text-muted mt-3 text-center">همه آنچه برای مدیریت حرفه‌ای شبکه‌های اجتماعی نیاز دارید</p>
            </div>
        </div>

        <div class="row g-4">
            <!-- Feature Items with animations -->
            <div class="col-md-4" data-aos="fade-up" data-aos-duration="800" data-aos-delay="100">
                <div class="feature-card h-100 p-4 text-center shadow rounded-4 bg-white position-relative">
                    <div class="feature-icon-wrapper mb-4 mx-auto">
                        <i class="fas fa-pen-fancy"></i>
                    </div>
                    <h4 class="fw-bold mb-3">تولید محتوا</h4>
                    <p class="text-muted">محتوای متنی، تصویری و ویدیویی با کمک هوش مصنوعی در چند ثانیه تولید کنید.</p>
                </div>
            </div>

            <div class="col-md-4" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
                <div class="feature-card h-100 p-4 text-center shadow rounded-4 bg-white position-relative">
                    <div class="feature-icon-wrapper mb-4 mx-auto">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h4 class="fw-bold mb-3">مدیریت کامنت‌ها</h4>
                    <p class="text-muted">پاسخگویی خودکار به کامنت‌ها و پیام‌ها با لحن شخصی‌سازی شده برند شما.</p>
                </div>
            </div>

            <div class="col-md-4" data-aos="fade-up" data-aos-duration="800" data-aos-delay="300">
                <div class="feature-card h-100 p-4 text-center shadow rounded-4 bg-white position-relative">
                    <div class="feature-icon-wrapper mb-4 mx-auto">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h4 class="fw-bold mb-3">داشبورد تحلیلی</h4>
                    <p class="text-muted">تحلیل عمیق عملکرد محتوا، احساسات مخاطبان و روند رشد صفحات.</p>
                </div>
            </div>

            <div class="col-md-4">
                <div class="feature-card h-100 p-4 text-center shadow rounded-4 bg-white position-relative">
                    <div class="feature-icon-wrapper mb-4 mx-auto">
                        <i class="fas fa-share-alt"></i>
                    </div>
                    <h4 class="fw-bold mb-3">انتشار همه‌جانبه</h4>
                    <p class="text-muted">یک بار تولید کنید، همه‌جا منتشر کنید - با فرمت مناسب هر پلتفرم.</p>
                </div>
            </div>

            <div class="col-md-4">
                <div class="feature-card h-100 p-4 text-center shadow rounded-4 bg-white position-relative">
                    <div class="feature-icon-wrapper mb-4 mx-auto">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h4 class="fw-bold mb-3">ایجنت‌های هوشمند</h4>
                    <p class="text-muted">دستیارهای هوشمند اختصاصی برای هر بخش از مدیریت محتوای شما.</p>
                </div>
            </div>

            <div class="col-md-4">
                <div class="feature-card h-100 p-4 text-center shadow rounded-4 bg-white position-relative">
                    <div class="feature-icon-wrapper mb-4 mx-auto">
                        <i class="fas fa-fingerprint"></i>
                    </div>
                    <h4 class="fw-bold mb-3">شخصیت برند</h4>
                    <p class="text-muted">حفظ هویت و لحن برند شما در تمامی محتواها و تعاملات.</p>
                </div>
            </div>
        </div>
    </div>
</section>


<section class="how-we-help-section py-5" style="background: linear-gradient(135deg, #e0f7fa, #ffffff);">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-lg-8 mx-auto">
                <h2 class="fw-bold text-center w-100">
                    رومینکست چطور به شما کمک می‌کند
                </h2>
                <p class="lead text-muted mt-3 text-center">در دنیای پرسرعت امروز، تولید و مدیریت محتوای حرفه‌ای یک چالش بزرگ است. رومینکست به عنوان یک دستیار هوشمند، این مسیر را برای شما هموار می‌کند:</p>
            </div>
        </div>

        <div class="row g-4">
            <!-- Smart Ideation -->
            <div class="col-md-6 col-lg-3">
                <div class="help-card-modern h-100 p-4 text-center">
                    <div class="help-icon mb-4 mx-auto">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h5 class="fw-semibold">ایده‌پردازی هوشمند</h5>
                    <p class="small text-muted mt-2">پیشنهاد ایده‌های تازه متناسب با مخاطب شما</p>
                </div>
            </div>

            <!-- Automated Content Creation -->
            <div class="col-md-6 col-lg-3">
                <div class="help-card-modern h-100 p-4 text-center">
                    <div class="help-icon mb-4 mx-auto">
                        <i class="fas fa-magic"></i>
                    </div>
                    <h5 class="fw-semibold">تولید خودکار محتوا</h5>
                    <p class="small text-muted mt-2">تولید متن، تصویر و پیام‌های تبلیغاتی با کیفیت بالا</p>
                </div>
            </div>

            <!-- Content Publishing and Scheduling -->
            <div class="col-md-6 col-lg-3">
                <div class="help-card-modern h-100 p-4 text-center">
                    <div class="help-icon mb-4 mx-auto">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <h5 class="fw-semibold">مدیریت و زمان‌بندی انتشار</h5>
                    <p class="small text-muted mt-2">برنامه‌ریزی و انتشار محتوا در کانال‌ها و شبکه‌های مختلف</p>
                </div>
            </div>

            <!-- Performance Analysis -->
            <div class="col-md-6 col-lg-3">
                <div class="help-card-modern h-100 p-4 text-center">
                    <div class="help-icon mb-4 mx-auto">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h5 class="fw-semibold">تحلیل عملکرد</h5>
                    <p class="small text-muted mt-2">بررسی بازخورد محتوا و بهینه‌سازی مداوم بر اساس داده‌های واقعی</p>
                </div>
            </div>

        </div>
    </div>
</section>

<!-- Social Media Integration Section -->
<section class="social-integration-section py-5 landing-page">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-4 mb-lg-0" data-aos="fade-right" data-aos-duration="1000">
                <h2 class="fw-bold mb-4">یکپارچه‌سازی هوشمند شبکه‌های اجتماعی</h2>
                <p class="lead mb-4">رومینکست به طور خودکار با تمامی پلتفرم‌های محبوب شبکه‌های اجتماعی ادغام می‌شود و مدیریت یکپارچه محتوا را امکان‌پذیر می‌سازد.</p>
                
                <div class="features-list">
                    <div class="d-flex align-items-center mb-3">
                        <div class="feature-icon-small me-3 text-primary">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <p class="mb-0">همگام‌سازی خودکار محتوا بین پلتفرم‌های مختلف</p>
                    </div>
                    
                    <div class="d-flex align-items-center mb-3">
                        <div class="feature-icon-small me-3 text-primary">
                            <i class="fas fa-clock"></i>
                        </div>
                        <p class="mb-0">زمان‌بندی انتشار محتوا در زمان‌های بهینه</p>
                    </div>
                    
                    <div class="d-flex align-items-center mb-3">
                        <div class="feature-icon-small me-3 text-primary">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <p class="mb-0">گزارش‌های تحلیلی یکپارچه از تمام پلتفرم‌ها</p>
                    </div>
                    
                    <div class="d-flex align-items-center mb-4">
                        <div class="feature-icon-small me-3 text-primary">
                            <i class="fas fa-comment-dots"></i>
                        </div>
                        <p class="mb-0">مدیریت متمرکز کامنت‌ها و پیام‌های مستقیم</p>
                    </div>
                </div>
              
            </div>
            
            <div class="col-lg-6 text-center" data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
                <div class="integration-animation-container position-relative">
                    <img src="/static/img/social-integration.png" alt="یکپارچه‌سازی شبکه‌های اجتماعی" class="img-fluid">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Who Can Use Rominext Section -->
<section class="who-can-use py-4 landing-page">
    <div class="container">
        <div class="row text-center mb-4">
            <div class="col-lg-8 mx-auto">
                <h2 class="fw-bold text-center">چه کسانی می‌توانند از رومینکست استفاده کنند؟</h2>
                <p class="lead text-center">رومینکست برای تمام افرادی که در حوزه محتوا و شبکه‌های اجتماعی فعالیت می‌کنند طراحی شده است</p>
            </div>
        </div>
        
        <div class="user-roles-diagram mx-auto">
            <!-- User roles in a spread pattern -->
            <div class="role-node role-sm-manager">
                <i class="fas fa-share-alt fa-2x text-success"></i>
                <div class="role-label">ادمین‌ها و مدیران پیج‌های اینستاگرام</div>
            </div>
            
            <div class="role-node role-content">
                <i class="fas fa-lightbulb fa-2x text-success"></i>
                <div class="role-label">تولیدکنندگان محتوا و فریلنسرها</div>
            </div>
            
            <div class="role-node role-copywriter">
                <i class="fas fa-pen-fancy fa-2x text-success"></i>
                <div class="role-label">آژانس‌های دیجیتال مارکتینگ</div>
            </div>
            
            <div class="role-node role-designer">
                <i class="fas fa-palette fa-2x text-success"></i>
                <div class="role-label">کسب‌وکارهای متوسط و بزرگ</div>
            </div>
            
            <div class="role-node role-analyst">
                <i class="fas fa-chart-line fa-2x text-success"></i>
                <div class="role-label">رسانه‌ها و کانال‌های تلگرام</div>
            </div>
            
            <div class="role-node role-engagement">
                <i class="fas fa-comments fa-2x text-success"></i>
                <div class="role-label">فعالان حوزه آموزش و مربیان آنلاین</div>
            </div>
            
            <div class="role-node role-startup">
                <i class="fas fa-rocket fa-2x text-success"></i>
                <div class="role-label">استارتاپ‌ها و پروژه‌های نوپا</div>
            </div>
        </div>
    </div>
</section>

<!-- Latest Blog Posts Section -->
<section class="latest-blog-posts py-5 landing-page" style="background-color: #f8f9fa;">
    <div class="container">
        <div class="row text-center mb-4">
            <div class="col-12">
                <a href="{{ url_for('landing.blog') }}" class="text-decoration-none text-dark">
                    <h2 class="fw-bold text-center blog-section-title">آخرین مقالات وبلاگ</h2>
                    <p class="lead text-center">جدیدترین مطالب و آموزش‌های ما را در وبلاگ رومینکست دنبال کنید</p>
                </a>
            </div>
        </div>

        <div class="row g-4">
            {% if latest_blogs %}
                {% for blog in latest_blogs %}
                <div class="col-md-6 col-lg-3">
                    <a href="{{ url_for('blog.view', slug=blog.slug) }}" class="text-decoration-none">
                        <div class="blog-card h-100 rounded-4 shadow-sm overflow-hidden bg-white">
                            <div class="blog-thumb position-relative">
                                {% if blog.featured_image %}
                                    <img src="{{ blog.featured_image }}" alt="{{ blog.title }}" class="img-fluid w-100">
                                {% else %}
                                    <img src="/static/img/default_blog_thumb.jpg" alt="{{ blog.title }}" class="img-fluid w-100">
                                {% endif %}
                            </div>
                            <div class="blog-content p-3">
                                <h5 class="blog-title fw-bold mb-2 text-dark">{{ blog.title }}</h5>
                                <p class="blog-excerpt text-muted small mb-3">{{ blog.content|striptags|truncate(100) }}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="blog-date small text-muted">
                                        <i class="far fa-calendar-alt me-1"></i>
                                        {{ blog.published_at.strftime('%Y/%m/%d') if blog.published_at else blog.created_at.strftime('%Y/%m/%d') }}
                                    </span>
                                    <span class="blog-read-more small">ادامه مطلب <i class="fas fa-arrow-left ms-1"></i></span>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                {% endfor %}
            {% else %}
                <!-- Fallback content when no blogs are available -->
                {% for i in range(4) %}
                <div class="col-md-6 col-lg-3">
                    <a href="{{ url_for('landing.blog') }}" class="text-decoration-none">
                        <div class="blog-card h-100 rounded-4 shadow-sm overflow-hidden bg-white">
                            <div class="blog-thumb position-relative">
                                <img src="/static/img/default_blog_thumb.jpg" alt="عنوان مقاله" class="img-fluid w-100">
                            </div>
                            <div class="blog-content p-3">
                                <h5 class="blog-title fw-bold mb-2 text-dark">به زودی مقالات جدید</h5>
                                <p class="blog-excerpt text-muted small mb-3">مقالات و آموزش‌های مفیدی در حال آماده‌سازی هستند...</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="blog-date small text-muted"><i class="far fa-calendar-alt me-1"></i> به زودی</span>
                                    <span class="blog-read-more small">ادامه مطلب <i class="fas fa-arrow-left ms-1"></i></span>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                {% endfor %}
            {% endif %}
        </div>

   

    </div>
</section>

<style>
    .blog-section-title {
        transition: color 0.3s ease;
    }

    .blog-section-title:hover {
        color: #00A0A0 !important;
    }

    .blog-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        cursor: pointer;
    }

    .blog-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
    }

    .blog-thumb {
        height: 180px;
        overflow: hidden;
    }

    .blog-thumb img {
        transition: transform 0.5s ease;
        height: 100%;
        object-fit: cover;
    }

    .blog-card:hover .blog-thumb img {
        transform: scale(1.05);
    }

    .blog-category {
        font-size: 0.75rem;
        font-weight: 600;
        color: #00A0A0;
    }

    .blog-title {
        font-size: 1.1rem;
        line-height: 1.4;
        height: 3rem;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .blog-excerpt {
        height: 4.5em;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
    }

    .blog-read-more {
        color: #00A0A0;
        font-weight: 600;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .blog-read-more:hover {
        color: #007a7a;
    }
</style>

<!-- Advanced Technologies Section -->
<section class="technologies-section py-5 bg-light landing-page">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-lg-8 mx-auto">
                <h2 class="fw-bold text-center">فناوری‌های پیشرفته در قلب رومینکست</h2>
                <p class="lead text-center">رومینکست بر پایه‌ی ترکیبی از به‌روزترین فناوری‌های هوش مصنوعی و مهندسی نرم‌افزار ساخته شده است:</p>
            </div>
        </div>
        
        <div class="row g-4">
            <!-- AI Agents -->
            <div class="col-md-6 col-lg-3">
                <div class="tech-card h-100 p-4 text-center">
                    <div class="feature-icon mb-3 mx-auto">
                        <i class="fas fa-robot text-success"></i>
                    </div>
                    <h4>عامل‌های هوشمند</h4>
                    <p>سیستم‌های خودمختار برای مدیریت وظایف پیچیده‌ی تولید و انتشار محتوا</p>
                </div>
            </div>

            <!-- LLMs -->
            <div class="col-md-6 col-lg-3">
                <div class="tech-card h-100 p-4 text-center">
                    <div class="feature-icon mb-3 mx-auto">
                        <i class="fas fa-brain text-success"></i>
                    </div>
                    <h4>مدل‌های زبان پیشرفته</h4>
                    <p>استفاده از قدرتمندترین مدل‌های زبانی نسل جدید برای تولید متون طبیعی و خلاقانه</p>
                </div>
            </div>
            
            <!-- Image APIs -->
            <div class="col-md-6 col-lg-3">
                <div class="tech-card h-100 p-4 text-center">
                    <div class="feature-icon mb-3 mx-auto">
                        <i class="fas fa-image text-success"></i>
                    </div>
                    <h4>اتصال به APIهای تصویرسازی</h4>
                    <p>تولید تصاویر و گرافیک‌های اختصاصی با هوش مصنوعی</p>
                </div>
            </div>
            
            <!-- Data-driven Automation -->
            <div class="col-md-6 col-lg-3">
                <div class="tech-card h-100 p-4 text-center">
                    <div class="feature-icon mb-3 mx-auto">
                        <i class="fas fa-database text-success"></i>
                    </div>
                    <h4>اتوماسیون داده‌محور</h4>
                    <p>تحلیل رفتار مخاطب و بهینه‌سازی خودکار استراتژی‌های محتوا</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Get Started Section -->
<section class="get-started-section py-5 landing-page">
    <div class="container">
        <div class="row text-center mb-4">
            <div class="col-lg-8 mx-auto" data-aos="fade-up" data-aos-duration="800">
                <h2 class="fw-bold text-center">همین امروز شروع کنید</h2>
                <p class="lead text-center">قدرت هوش مصنوعی را در مدیریت محتوای خود تجربه کنید</p>
            </div>
        </div>
        
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6 text-center" data-aos="zoom-in" data-aos-duration="1000" data-aos-delay="200">
                <div class="start-card p-5 rounded-4 shadow position-relative overflow-hidden">
                    <!-- Decorative elements -->
                    <div class="start-card-decoration decoration-1"></div>
                    <div class="start-card-decoration decoration-2"></div>
                    
                    <div class="mb-4 rocket-icon-wrapper">
                        <i class="fas fa-rocket fa-3x text-primary"></i>
                    </div>
                    <h3 class="text-center mb-3">رومینکست را تجربه کنید</h3>
                    <p class="text-center mb-4">همین حالا حساب کاربری خود را بسازید و قابلیت‌های هوشمند رومینکست را کشف کنید.</p>
                    <div class="text-center">
                        <a href="{{ url_for('vault.register') }}" class="btn btn-primary btn-lg rounded-pill px-5 start-button">
                            <span class="start-button-text">شروع کنید</span>
                            <span class="start-button-icon"><i class="fas fa-arrow-right"></i></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Premium CTA Section CSS - Isolated from other sections -->


<style>
    .modern-comparison-section {
        padding: 120px 0 100px 0; /* Increased top padding */
        min-height: 600px;
    }
    
    .diagonal-split {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 150px;
        background-color: white;
        clip-path: polygon(0 100%, 100% 0, 100% 100%, 0% 100%);
    }
    
    .illustration-placeholder {
        position: relative;
        height: 350px;
    }
    
    .circle-bg {
        position: absolute;
        width: 350px;
        height: 350px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    
    .btn-light {
        border: 2px solid white;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-light:hover {
        background-color: transparent;
        color: white;
    }
</style>

<style>
/* Premium CTA Section Base */
.premium-cta-section {
    position: relative;
    padding: 60px 0;
    border-radius: 20px;
    margin: 40px 0;
    overflow: hidden;
    background: transparent; /* Changed from gradient to transparent */
}

/* Wrapper styling */
.premium-cta-wrapper {
    background: rgba(230,242,255,0.8); /* Light blue semi-transparent background */
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.05);
    border: 1px solid rgba(200,225,255,0.8); /* Light blue border */
    backdrop-filter: blur(5px);
    position: relative;
    z-index: 1;
    transition: background 0.3s, box-shadow 0.3s;
}

/* When hovered */
.premium-cta-wrapper:hover {
    background: rgba(220,237,255,0.85); /* Slightly darker on hover */
    box-shadow: 0 12px 35px rgba(0,0,0,0.08);
}

/* Animated particles */
.premium-particle {
    position: absolute;
    border-radius: 50%;
    z-index: 0;
    opacity: 0.6;
}

.particle1 {
    width: 120px;
    height: 120px;
    background: linear-gradient(45deg, #64b5f6, #42a5f5);
    top: -30px;
    left: 10%;
    animation: float-premium 12s infinite alternate;
}

.particle2 {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, #81c784, #66bb6a);
    bottom: -20px;
    right: 15%;
    animation: float-premium 10s infinite alternate-reverse;
}

.particle3 {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #ffb74d, #ffa726);
    top: 30%;
    right: 5%;
    animation: float-premium 8s infinite alternate;
}

.particle4 {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #e57373, #ef5350);
    bottom: 20%;
    left: 10%;
    animation: float-premium 9s infinite alternate-reverse;
}

.particle5 {
    width: 30px;
    height: 30px;
    background: linear-gradient(45deg, #ba68c8, #ab47bc);
    top: 20%;
    left: 20%;
    animation: float-premium 7s infinite alternate;
}

@keyframes float-premium {
    0% {
        transform: translate(0, 0) rotate(0deg);
    }
    100% {
        transform: translate(15px, 15px) rotate(10deg);
    }
}

/* Icon styling */
.premium-icon-container {
    display: inline-block;
}

.premium-icon-circle {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4fc3f7, #29b6f6);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    border-radius: 50%;
    margin: 0 auto;
    box-shadow: 0 10px 20px rgba(41, 182, 246, 0.3);
    position: relative;
    z-index: 2;
    animation: pulse-premium 2s infinite;
}

@keyframes pulse-premium {
    0% {
        box-shadow: 0 0 0 0 rgba(41, 182, 246, 0.4);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(41, 182, 246, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(41, 182, 246, 0);
    }
}

/* Text styling */
.premium-title {
    color: #2c3e50;
    font-size: 2.2rem;
    position: relative;
    display: inline-block;
}

.premium-subtitle {
    color: #546e7a;
    font-size: 1.2rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

/* Button styling */
.premium-button-container {
    display: inline-block;
    position: relative;
    z-index: 2;
}

.premium-cta-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 15px 40px;
    background: linear-gradient(45deg, #42a5f5, #2196f3);
    color: white;
    border-radius: 50px;
    font-weight: bold;
    font-size: 18px;
    text-decoration: none;
    transition: transform 0.3s, box-shadow 0.3s;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(33, 150, 243, 0.3);
}

.premium-cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
}

.premium-button-text {
    position: relative;
    z-index: 2;
    margin-right: 10px;
}

.premium-button-icon {
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s, background 0.3s;
}

.premium-cta-button:hover .premium-button-icon {
    transform: translateX(5px);
    background: rgba(255, 255, 255, 0.3);
}

/* --- Decorative Underline --- */
.title-underline {
    display: block;
    width: 80px;
    height: 4px;
    background: #00acc1;
    margin: 10px auto 0;
    border-radius: 2px;
}

/* --- Feature Cards --- */
.feature-icon-wrapper {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #26c6da, #00acc1);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    border-radius: 50%;
    box-shadow: 0 4px 15px rgba(0, 188, 212, 0.3);
    transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover .feature-icon-wrapper {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 20px rgba(0, 188, 212, 0.5);
}

.feature-card {
    transition: transform 0.3s, box-shadow 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* --- Help Cards --- */
.help-card-modern {
    background: rgba(255, 255, 255, 0.6);
    border: 1px solid #b2ebf2;
    border-radius: 16px;
    backdrop-filter: blur(10px);
    transition: border-color 0.3s, box-shadow 0.3s, transform 0.3s;
}

.help-card-modern:hover {
    border-color: #00acc1;
    box-shadow: 0 6px 20px rgba(0, 188, 212, 0.25);
    transform: translateY(-5px);
}

.help-icon {
    width: 60px;
    height: 60px;
    background: #00acc1;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    border-radius: 12px;
    transition: background 0.3s;
}

.help-card-modern:hover .help-icon {
    background: #007c91;
}

/* --- Technologies Section --- */
.technologies-section {
    background: #f8f9fa;
    padding: 4rem 0;
}

.tech-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
}

.tech-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.feature-icon {
    font-size: 3rem;
    color: #28a745;
}

.tech-card h4 {
    margin-top: 20px;
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
}

.tech-card p {
    font-size: 0.95rem;
    color: #555;
    margin-top: 10px;
}

@media (max-width: 767px) {
    .tech-card {
        padding: 15px;
    }
    
    .feature-icon {
        font-size: 2.5rem;
    }
}

/* --- Who Can Use Section --- */
.who-can-use {
    background: #f8f9fa;
    padding: 30px 0;
}

.user-roles-diagram {
    height: 350px;
    width: 100%;
    max-width: 1000px;
    position: relative;
    margin: 20px auto;
}

.role-node {
    position: absolute;
    text-align: center;
    width: 180px;
    transition: transform 0.3s;
    z-index: 2;
}

.role-node:hover {
    transform: scale(1.05);
}

.role-node i {
    margin-bottom: 10px;
}

.role-label {
    color: #333;
    font-weight: bold;
    font-size: 14px;
}

/* Position nodes in a spread pattern */
.role-sm-manager { top: 10%; left: 25%; }
.role-content { top: 10%; left: 65%; }
.role-copywriter { top: 40%; left: 10%; }
.role-designer { top: 40%; left: 80%; }
.role-analyst { top: 75%; left: 25%; }
.role-engagement { top: 75%; left: 65%; }
.role-startup { top: 40%; left: 45%; }

@media (max-width: 992px) {
    .user-roles-diagram {
        height: 500px;
    }
    
    .role-node {
        width: 160px;
    }
    
    .role-sm-manager { top: 10%; left: 20%; }
    .role-content { top: 10%; left: 60%; }
    .role-copywriter { top: 30%; left: 5%; }
    .role-designer { top: 30%; left: 75%; }
    .role-analyst { top: 70%; left: 20%; }
    .role-engagement { top: 70%; left: 60%; }
    .role-startup { top: 50%; left: 40%; }
}

@media (max-width: 768px) {
    .user-roles-diagram {
        height: 700px;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    
    .role-node {
        position: relative;
        top: auto !important;
        left: auto !important;
        transform: none !important;
        margin-bottom: 20px;
        width: 220px;
    }
    
    .role-sm-manager { order: 1; }
    .role-content { order: 2; }
    .role-copywriter { order: 3; }
    .role-designer { order: 4; }
    .role-analyst { order: 5; }
    .role-engagement { order: 6; }
    .role-startup { order: 7; }
}
</style>

<style>
    /* Get Started Section Styling */
    .get-started-section {
        position: relative;
        overflow: hidden;
    }
    
    .start-card {
        background: white;
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 160, 160, 0.1);
    }
    
    .start-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1) !important;
    }
    
    .start-card-decoration {
        position: absolute;
        border-radius: 50%;
        background: linear-gradient(135deg, rgba(0, 160, 160, 0.1), rgba(0, 160, 160, 0.05));
        z-index: 0;
    }
    
    .decoration-1 {
        width: 200px;
        height: 200px;
        top: -100px;
        right: -100px;
    }
    
    .decoration-2 {
        width: 150px;
        height: 150px;
        bottom: -75px;
        left: -75px;
    }
    
    .rocket-icon-wrapper {
        position: relative;
        width: 90px;
        height: 90px;
        background: linear-gradient(135deg, #e0f2f2, #f0f7fa);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        transition: all 0.3s ease;
        box-shadow: 0 10px 20px rgba(0, 160, 160, 0.15);
    }
    
    .start-card:hover .rocket-icon-wrapper {
        transform: translateY(-5px) rotate(10deg);
        box-shadow: 0 15px 25px rgba(0, 160, 160, 0.25);
    }
    
    .start-button {
        position: relative;
        overflow: hidden;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #00A0A0, #008080);
        border: none;
        box-shadow: 0 5px 15px rgba(0, 160, 160, 0.3);
    }
    
    .start-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 160, 160, 0.4);
        background: linear-gradient(135deg, #008080, #006666);
    }
    
    .start-button-text {
        position: relative;
        z-index: 2;
        transition: all 0.3s ease;
    }
    
    .start-button-icon {
        width: 30px;
        height: 30px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        margin-right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }
    
    .start-button:hover .start-button-icon {
        transform: translateX(5px);
        background: rgba(255, 255, 255, 0.3);
    }
</style>

{% endblock %}

{% block scripts %}
<!-- AOS JS -->
<script src="https://unpkg.com/aos@next/dist/aos.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize AOS
        AOS.init({
            offset: 120, // offset (in px) from the original trigger point
            delay: 0, // values from 0 to 3000, with step 50ms
            duration: 800, // values from 0 to 3000, with step 50ms
            easing: 'ease', // default easing for AOS animations
            once: true, // whether animation should happen only once - while scrolling down
            mirror: false, // whether elements should animate out while scrolling past them
            anchorPlacement: 'top-bottom', // defines which position of the element regarding to window should trigger the animation
        });
    });
</script>
{% endblock %}
