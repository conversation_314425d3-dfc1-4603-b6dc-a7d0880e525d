from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import Optional, Dict, Any, List, ClassVar
import os
from functools import lru_cache
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Settings(BaseSettings):
    """Application settings"""
    # API Keys
    FACEBOOK_PAGE_ACCESS_TOKEN: str = ""
    FACEBOOK_PAGE_ID: str = ""
    GROQ_API_KEY: str = ""
    OPENAI_API_KEY: str = ""
    DEEPSEEK_API_KEY: str = ""
    GROK_API_KEY: str = ""
    HUGGINGFACE_API_KEY: str = ""
    TOGETHER_API_KEY: str = ""
    STABILITY_API_KEY: str = ""
    DEEPINFRA_API_KEY: str = ""
    TELEGRAM_BOT_TOKEN: str = ""
    
    # Security Settings
    SECRET_KEY: str = "your-secret-key-here"
    
    # App Settings
    MONGODB_URI: str = "mongodb://localhost:27017/Rominext"
    ENABLE_SWAGGER: bool = False  # Default to False for production
    DEFAULT_AI_PROVIDER: str = "openai"
    
    # MongoDB settings
    MONGODB_DB: str = "rominext"  # Ensure lowercase throughout the application
    MONGODB_HOST: str = "localhost"
    MONGODB_PORT: int = 27017
    MONGODB_USERNAME: str = ""  # Add if authentication is required
    MONGODB_PASSWORD: str = ""  # Add if authentication is required
    
    # Configure Pydantic to allow extra fields from .env
    model_config = SettingsConfigDict(
        env_file=".env",
        extra="ignore"  # This allows extra fields in the .env file
    )

class TestConfig(Settings):
    """Test configuration settings"""
    TESTING: ClassVar[bool] = True
    WTF_CSRF_ENABLED: ClassVar[bool] = False
    MONGODB_SETTINGS: ClassVar[Dict[str, Any]] = {
        'db': 'test_db',
        'host': 'mongomock://localhost'
    }
    SECRET_KEY: ClassVar[str] = 'test-secret-key'
    DEBUG: ClassVar[bool] = True

@lru_cache()
def get_settings() -> Settings:
    return Settings()



