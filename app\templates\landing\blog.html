{% extends "main_layout.html" %}

{% block title %}وبلاگ رومینکست - آخرین مقالات و آموزش‌ها{% endblock %}

{% block content %}
<!-- Blog Header Section -->
<section class="blog-hero-section position-relative overflow-hidden">
    <div class="blog-hero-bg"></div>
    <div class="container py-5 position-relative">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <div class="blog-hero-content">
                    <h1 class="display-3 fw-bold text-dark mb-4">وبلاگ رومینکست</h1>
                    <p class="lead text-muted mb-4 fs-5">آخرین مقالات، آموزش‌ها و نکات کاربردی در زمینه تولید محتوا و هوش مصنوعی</p>
                    {% if total %}
                    <div class="blog-stats d-inline-flex align-items-center bg-white rounded-pill px-4 py-2 shadow-sm">
                        <i class="fas fa-newspaper text-primary me-2"></i>
                        <span class="fw-semibold text-dark">{{ total }} مقاله منتشر شده</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    <!-- Decorative elements -->
    <div class="blog-hero-decoration">
        <div class="decoration-circle decoration-circle-1"></div>
        <div class="decoration-circle decoration-circle-2"></div>
        <div class="decoration-circle decoration-circle-3"></div>
    </div>
</section>

<div class="container py-5">

    <div class="row g-4">
        {% if blogs %}
            {% for blog in blogs %}
                <div class="col-sm-6 col-md-4 col-lg-3 mb-4">
                    <a href="{{ url_for('blog.view', slug=blog.slug) }}" class="text-decoration-none">
                        <div class="blog-card h-100 rounded-4 shadow-sm overflow-hidden bg-white">
                            <div class="blog-thumb position-relative">
                                {% if blog.featured_image %}
                                    <img src="{{ blog.featured_image }}" class="img-fluid w-100" alt="{{ blog.title }}">
                                {% else %}
                                    <img src="/static/img/default_blog_thumb.jpg" class="img-fluid w-100" alt="{{ blog.title }}">
                                {% endif %}
                            </div>
                            <div class="blog-content p-3">
                                <h5 class="blog-title fw-bold mb-2 text-dark">{{ blog.title }}</h5>
                                {% if blog.published_at %}
                                <p class="blog-date small text-muted mb-2">
                                    <i class="far fa-calendar-alt me-1"></i>
                                    {{ blog.published_at.strftime('%Y/%m/%d') }}
                                </p>
                                {% endif %}
                                <p class="blog-excerpt text-muted small mb-3">{{ blog.content|striptags|truncate(120) }}</p>
                                <span class="blog-read-more small">
                                    ادامه مطلب <i class="fas fa-arrow-left ms-1"></i>
                                </span>
                            </div>
                            {% if blog.tags %}
                            <div class="blog-footer p-3 pt-0">
                                {% for tag in blog.tags[:3] %}
                                    <span class="badge bg-light text-dark me-1 small">{{ tag }}</span>
                                {% endfor %}
                                {% if blog.tags|length > 3 %}
                                    <span class="badge bg-light text-muted small">+{{ blog.tags|length - 3 }}</span>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                    </a>
                </div>
            {% endfor %}
        {% else %}
            <div class="col-12 text-center py-5">
                <div class="empty-state">
                    <i class="fas fa-newspaper fa-4x text-muted mb-3"></i>
                    <h3 class="text-muted">هنوز مقاله‌ای منتشر نشده است</h3>
                    <p class="text-muted">به زودی مقالات جدید و مفیدی در این بخش منتشر خواهد شد.</p>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Pagination -->
    {% if pages > 1 %}
    <div class="row mt-5">
        <div class="col-12">
            <!-- Pagination Info -->
            <div class="text-center mb-3">
                <p class="text-muted small">
                    صفحه {{ page }} از {{ pages }}
                    ({{ ((page-1) * per_page + 1) }} تا {{ [page * per_page, total]|min }} از {{ total }} مقاله)
                </p>
            </div>

            <nav aria-label="صفحه‌بندی مقالات">
                <ul class="pagination justify-content-center">
                    <!-- First Page -->
                    {% if page > 2 %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('landing.blog', page=1) }}" title="صفحه اول">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    {% endif %}

                    <!-- Previous Page -->
                    {% if page > 1 %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('landing.blog', page=page-1) }}" aria-label="صفحه قبل" title="صفحه قبل">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    {% endif %}

                    <!-- Page Numbers -->
                    {% set start_page = [1, page - 2]|max %}
                    {% set end_page = [pages, page + 2]|min %}

                    {% if start_page > 1 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}

                    {% for p in range(start_page, end_page + 1) %}
                    <li class="page-item {% if p == page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('landing.blog', page=p) }}">{{ p }}</a>
                    </li>
                    {% endfor %}

                    {% if end_page < pages %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}

                    <!-- Next Page -->
                    {% if page < pages %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('landing.blog', page=page+1) }}" aria-label="صفحه بعد" title="صفحه بعد">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    {% endif %}

                    <!-- Last Page -->
                    {% if page < pages - 1 %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('landing.blog', page=pages) }}" title="صفحه آخر">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>

<style>
    /* Blog Hero Section Styles */
    .blog-hero-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        min-height: 400px;
        display: flex;
        align-items: center;
        margin-bottom: 2rem;
    }

    .blog-hero-bg {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23ffffff" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="%23ffffff" opacity="0.05"/><circle cx="10" cy="60" r="0.5" fill="%23ffffff" opacity="0.05"/><circle cx="90" cy="40" r="0.5" fill="%23ffffff" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .blog-hero-content {
        animation: fadeInUp 0.8s ease-out;
    }

    .blog-icon {
        animation: pulse 2s infinite;
    }

    .blog-stats {
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        animation: slideInUp 0.8s ease-out 0.3s both;
    }

    .blog-hero-decoration {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        overflow: hidden;
    }

    .decoration-circle {
        position: absolute;
        border-radius: 50%;
        background: linear-gradient(45deg, rgba(0, 160, 160, 0.1), rgba(0, 160, 160, 0.05));
        animation: float 6s ease-in-out infinite;
    }

    .decoration-circle-1 {
        width: 120px;
        height: 120px;
        top: 10%;
        right: 10%;
        animation-delay: 0s;
    }

    .decoration-circle-2 {
        width: 80px;
        height: 80px;
        bottom: 20%;
        left: 15%;
        animation-delay: 2s;
    }

    .decoration-circle-3 {
        width: 60px;
        height: 60px;
        top: 60%;
        right: 25%;
        animation-delay: 4s;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
        }
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px) rotate(0deg);
        }
        33% {
            transform: translateY(-10px) rotate(120deg);
        }
        66% {
            transform: translateY(5px) rotate(240deg);
        }
    }

    /* Blog Card Styles */
    .blog-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        cursor: pointer;
        border: none;
    }

    .blog-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
    }

    .blog-thumb {
        height: 200px;
        overflow: hidden;
    }

    .blog-thumb img {
        transition: transform 0.5s ease;
        height: 100%;
        object-fit: cover;
    }

    .blog-card:hover .blog-thumb img {
        transform: scale(1.05);
    }

    .blog-title {
        font-size: 1rem;
        line-height: 1.4;
        height: 2.8rem;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        color: #333;
    }

    .blog-excerpt {
        height: 4rem;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        font-size: 0.85rem;
    }

    .blog-read-more {
        color: #00A0A0;
        font-weight: 600;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .blog-read-more:hover {
        color: #007a7a;
    }

    .pagination .page-link {
        color: #00A0A0;
        border-color: #dee2e6;
        padding: 0.5rem 0.75rem;
    }

    .pagination .page-link:hover {
        color: #007a7a;
        background-color: #e9ecef;
        border-color: #dee2e6;
    }

    .pagination .page-item.active .page-link {
        background-color: #00A0A0;
        border-color: #00A0A0;
    }

    .empty-state {
        padding: 3rem 0;
    }

    @media (max-width: 768px) {
        .blog-card {
            margin-bottom: 1.5rem;
        }

        .blog-thumb {
            height: 180px;
        }

        .pagination .page-link {
            padding: 0.375rem 0.5rem;
            font-size: 0.875rem;
        }
    }
</style>
{% endblock %}

