import os
import uuid
from typing import List, Optional, Dict, Any
from werkzeug.utils import secure_filename
from datetime import datetime
import logging
from flask import current_app
from ..models.asset import Asset, AssetType

logger = logging.getLogger(__name__)

class AssetService:
    @staticmethod
    def upload_asset(
        user_id: str,
        file,
        file_type: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Asset:
        """
        Upload a new asset
        
        Args:
            user_id: The ID of the user uploading the asset
            file: The file object to upload
            file_type: The type of asset (must be a valid AssetType enum value)
            metadata: Optional metadata for the asset
            
        Returns:
            The created Asset object
        """
        # Validate file type
        if file_type not in [t.value for t in AssetType]:
            raise ValueError(f"Invalid file type. Must be one of: {', '.join([t.value for t in AssetType])}")
        
        # Generate a secure filename
        original_filename = secure_filename(file.filename)
        file_extension = os.path.splitext(original_filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        
        # Determine upload directory based on file type
        upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], file_type)
        os.makedirs(upload_dir, exist_ok=True)
        
        # Save the file
        file_path = os.path.join(upload_dir, unique_filename)
        file.save(file_path)
        
        # Generate file URL
        file_url = f"/uploads/{file_type}/{unique_filename}"
        
        # Prepare metadata
        if metadata is None:
            metadata = {}
            
        # Add file information to metadata
        metadata.update({
            'original_filename': original_filename,
            'file_size': os.path.getsize(file_path),
            'file_extension': file_extension
        })
        
        # Create asset object
        asset = Asset(
            user=user_id,
            file_url=file_url,
            file_type=file_type,
            metadata=metadata
        )
        
        asset.save()
        return asset
    
    @staticmethod
    def get_all_assets_for_user(user_id: str, file_type: Optional[str] = None) -> List[Asset]:
        """
        Get all assets for a user, optionally filtered by type
        
        Args:
            user_id: The ID of the user
            file_type: Optional filter by asset type
            
        Returns:
            List of Asset objects
        """
        query = {'user': user_id}
        
        if file_type:
            if file_type not in [t.value for t in AssetType]:
                raise ValueError(f"Invalid file type. Must be one of: {', '.join([t.value for t in AssetType])}")
            query['file_type'] = file_type
            
        return Asset.objects(**query).order_by('-created_at')
    
    @staticmethod
    def get_asset_by_id(asset_id: str) -> Optional[Asset]:
        """
        Get a specific asset by ID
        
        Args:
            asset_id: The ID of the asset
            
        Returns:
            Asset object or None if not found
        """
        try:
            return Asset.objects.get(id=asset_id)
        except Asset.DoesNotExist:
            logger.error(f"Asset with ID {asset_id} not found")
            return None
    
    @staticmethod
    def delete_asset(asset_id: str) -> bool:
        """
        Delete an asset by ID
        
        Args:
            asset_id: The ID of the asset to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            asset = Asset.objects.get(id=asset_id)
            
            # Get the file path from the URL
            file_path = os.path.join(
                current_app.config['UPLOAD_FOLDER'],
                asset.file_url.lstrip('/')
            )
            
            # Delete the file if it exists
            if os.path.exists(file_path):
                os.remove(file_path)
            
            # Delete the asset from the database
            asset.delete()
            return True
        except Asset.DoesNotExist:
            logger.error(f"Asset with ID {asset_id} not found")
            return False
        except Exception as e:
            logger.error(f"Error deleting asset: {str(e)}")
            return False
    
    @staticmethod
    def update_metadata(asset_id: str, metadata: Dict[str, Any]) -> Optional[Asset]:
        """
        Update the metadata of an asset
        
        Args:
            asset_id: The ID of the asset
            metadata: The new metadata to set
            
        Returns:
            Updated Asset object or None if not found
        """
        try:
            asset = Asset.objects.get(id=asset_id)
            
            # Update metadata
            asset.metadata.update(metadata)
            asset.updated_at = datetime.utcnow()
            asset.save()
            
            return asset
        except Asset.DoesNotExist:
            logger.error(f"Asset with ID {asset_id} not found")
            return None